import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../../enums.dart';
import 'box_provider.dart';

class PrefProvider {
  final BoxProvider boxProvider;
  final DeviceInfoPlugin deviceInfo;
  final PackageInfo packageInfo;

  // 响应式主题模式
  final _currentThemeMode = ThemeMode.system.obs;

  PrefProvider({
    required this.boxProvider,
    required this.deviceInfo,
    required this.packageInfo,
  }) {
    _loadThemeMode();
  }

  /// 获取当前主题模式（响应式）
  ThemeMode get currentThemeMode => _currentThemeMode.value;

  set themeMode(ThemeMode value) {
    // 更新响应式变量
    _currentThemeMode.value = value;
    // 持久化存储
    boxProvider.getGsBox(Boxes.settings.name).write('themeMode', value.name);
    // 更新应用主题
    Get.changeThemeMode(value);
  }

  ThemeMode get themeMode {
    final mode = boxProvider.getGsBox(Boxes.settings.name).read('themeMode');
    if (mode == null) return ThemeMode.system;
    return ThemeMode.values.firstWhere(
      (e) => e.name == mode,
      orElse: () => ThemeMode.system,
    );
  }

  /// 加载保存的主题模式
  void _loadThemeMode() {
    final mode = boxProvider.getGsBox(Boxes.settings.name).read('themeMode');
    if (mode != null) {
      final themeMode = ThemeMode.values.firstWhere(
        (e) => e.name == mode,
        orElse: () => ThemeMode.system,
      );
      // 更新响应式变量
      _currentThemeMode.value = themeMode;
      // 更新应用主题
      Future(() => Get.changeThemeMode(themeMode));
    } else {
      // 如果没有保存的主题模式，使用系统默认
      _currentThemeMode.value = ThemeMode.system;
    }
  }
}
